# Overview

This is a full-stack price scraping application that monitors product prices across multiple websites. The application allows users to add product URLs with CSS selectors to track prices over time. It provides a dashboard interface for managing scrapers, viewing current prices, and tracking price history. The system automatically scrapes websites to extract price information and maintains historical data for price tracking.

# User Preferences

Preferred communication style: Simple, everyday language.

# System Architecture

## Frontend Architecture
- **Framework**: React with TypeScript using Vite as the build tool
- **UI Components**: Built with shadcn/ui component library and Radix UI primitives
- **Styling**: Tailwind CSS with custom design system supporting light/dark themes
- **State Management**: React Query (@tanstack/react-query) for server state and caching
- **Routing**: Wouter for lightweight client-side routing
- **Form Handling**: React Hook Form with Zod validation for type-safe form management

## Backend Architecture
- **Runtime**: Node.js with Express.js framework
- **Language**: TypeScript with ESM modules
- **API Design**: RESTful API endpoints for CRUD operations on scrapers
- **Web Scraping**: Cheerio library for HTML parsing and price extraction
- **Data Storage**: In-memory storage implementation with interface for future database integration
- **Request Logging**: Custom middleware for API request/response logging

## Data Layer
- **ORM**: Drizzle ORM configured for PostgreSQL
- **Database**: PostgreSQL (via Neon serverless) with connection pooling
- **Schema**: Two main tables - scrapers for tracking configurations and price_history for historical data
- **Validation**: Zod schemas for runtime type validation shared between frontend and backend

## Development Workflow
- **Development Server**: Vite dev server with Express API proxy
- **Build Process**: Separate builds for client (Vite) and server (esbuild)
- **Type Safety**: Shared TypeScript schemas between client and server
- **Hot Reload**: Vite HMR for frontend, tsx for backend development

## Key Design Patterns
- **Shared Schema**: Common TypeScript types and Zod schemas in `/shared` directory
- **Component Architecture**: Modular UI components with consistent design patterns
- **Error Handling**: Centralized error handling with toast notifications
- **Real-time Updates**: Auto-refresh functionality for live price monitoring
- **Responsive Design**: Mobile-first approach with grid-based layouts

# External Dependencies

## Core Framework Dependencies
- **React Ecosystem**: React 18 with TypeScript, Vite for bundling, Wouter for routing
- **Backend Framework**: Express.js with TypeScript support via tsx

## UI and Styling
- **Component Library**: Radix UI primitives (@radix-ui/*) for accessible components
- **UI Framework**: shadcn/ui component system with Tailwind CSS
- **Icons**: Lucide React for consistent iconography
- **Styling**: Tailwind CSS with PostCSS processing

## Data Management
- **Database**: Neon PostgreSQL serverless database (@neondatabase/serverless)
- **ORM**: Drizzle ORM with Drizzle Kit for migrations
- **Validation**: Zod for runtime type validation and schema definition
- **State Management**: TanStack React Query for server state caching

## Web Scraping
- **HTML Parsing**: Cheerio for server-side HTML parsing and CSS selector queries
- **HTTP Requests**: Native Fetch API with custom headers for web scraping

## Development Tools
- **Build Tools**: Vite for frontend, esbuild for backend bundling
- **Type Checking**: TypeScript with strict mode enabled
- **Development**: Replit-specific plugins for error handling and cartography
- **Utilities**: date-fns for date manipulation, clsx for conditional styling