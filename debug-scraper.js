import { gotScraping } from "got-scraping";
import { chromium } from "playwright";
import * as cheerio from "cheerio";

const url = "https://www.memoryexpress.com/Products/MX00133178";

console.log("Testing Memory Express scraping...");

// Test with got-scraping
console.log("\n=== Testing got-scraping ===");
try {
  const response = await gotScraping.get(url, {
    timeout: { request: 15000 },
    headers: {
      'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
      'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
      'Accept-Language': 'en-US,en;q=0.5',
      'Accept-Encoding': 'gzip, deflate, br',
      'DNT': '1',
      'Connection': 'keep-alive',
      'Upgrade-Insecure-Requests': '1',
    },
    retry: {
      limit: 2,
      methods: ['GET']
    },
    followRedirect: true,
    maxRedirects: 5
  });
  
  console.log("Got-scraping response status:", response.statusCode);
  console.log("Got-scraping response length:", response.body.length);
  
  const $ = cheerio.load(response.body);
  
  // Look for common price selectors
  const priceSelectors = [
    '.price',
    '.product-price',
    '.current-price',
    '[data-price]',
    '.price-current',
    '.sale-price',
    '.regular-price'
  ];
  
  console.log("Searching for price elements...");
  priceSelectors.forEach(selector => {
    const elements = $(selector);
    if (elements.length > 0) {
      console.log(`Found ${elements.length} elements with selector "${selector}"`);
      elements.each((i, el) => {
        const text = $(el).text().trim();
        if (text && text.length < 50) {
          console.log(`  [${i}]: "${text}"`);
        }
      });
    }
  });
  
  // Look for any element containing dollar signs
  const dollarElements = $('*:contains("$")');
  console.log(`Found ${dollarElements.length} elements containing "$"`);
  
  // Show first few that look like prices
  let priceCount = 0;
  dollarElements.each((i, el) => {
    if (priceCount >= 5) return false;
    const text = $(el).text().trim();
    if (text.match(/\$[\d,]+\.?\d*/)) {
      console.log(`  Price-like text: "${text}"`);
      console.log(`  Element: ${el.tagName}.${$(el).attr('class') || ''}`);
      priceCount++;
    }
  });
  
} catch (error) {
  console.error("Got-scraping error:", error.message);
}

// Test with Playwright
console.log("\n=== Testing Playwright ===");
let browser;
try {
  browser = await chromium.launch({ headless: true });
  const page = await browser.newPage();
  
  await page.setUserAgent('Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36');
  await page.setViewportSize({ width: 1280, height: 720 });
  
  console.log("Navigating to page...");
  await page.goto(url, { waitUntil: 'domcontentloaded', timeout: 20000 });
  
  console.log("Waiting for page to load...");
  await page.waitForTimeout(3000);
  
  const html = await page.content();
  console.log("Playwright response length:", html.length);
  
  const $ = cheerio.load(html);
  
  // Look for price elements
  const priceSelectors = [
    '.price',
    '.product-price',
    '.current-price',
    '[data-price]',
    '.price-current',
    '.sale-price',
    '.regular-price'
  ];
  
  console.log("Searching for price elements...");
  priceSelectors.forEach(selector => {
    const elements = $(selector);
    if (elements.length > 0) {
      console.log(`Found ${elements.length} elements with selector "${selector}"`);
      elements.each((i, el) => {
        const text = $(el).text().trim();
        if (text && text.length < 50) {
          console.log(`  [${i}]: "${text}"`);
        }
      });
    }
  });
  
  // Look for any element containing dollar signs
  const dollarElements = $('*:contains("$")');
  console.log(`Found ${dollarElements.length} elements containing "$"`);
  
  // Show first few that look like prices
  let priceCount = 0;
  dollarElements.each((i, el) => {
    if (priceCount >= 5) return false;
    const text = $(el).text().trim();
    if (text.match(/\$[\d,]+\.?\d*/)) {
      console.log(`  Price-like text: "${text}"`);
      console.log(`  Element: ${el.tagName}.${$(el).attr('class') || ''}`);
      priceCount++;
    }
  });
  
} catch (error) {
  console.error("Playwright error:", error.message);
} finally {
  if (browser) {
    await browser.close();
  }
}

console.log("\nDone!");
