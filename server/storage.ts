import { type Scraper, type InsertScraper, type UpdateScraper, type PriceHistory } from "@shared/schema";
import { randomUUID } from "crypto";
import { JSONFilePreset } from "lowdb/node";

export interface IStorage {
  // Scraper operations
  getScraper(id: string): Promise<Scraper | undefined>;
  getAllScrapers(): Promise<Scraper[]>;
  createScraper(scraper: InsertScraper): Promise<Scraper>;
  updateScraper(id: string, updates: UpdateScraper): Promise<Scraper | undefined>;
  deleteScraper(id: string): Promise<boolean>;
  
  // Price history operations
  addPriceHistory(scraperId: string, price: string): Promise<void>;
  getPriceHistory(scraperId: string): Promise<PriceHistory[]>;
}

interface DatabaseSchema {
  scrapers: Scraper[];
  priceHistory: PriceHistory[];
}

export class JsonStorage implements IStorage {
  private db: Awaited<ReturnType<typeof JSONFilePreset<DatabaseSchema>>> | null = null;

  constructor() {
    this.init();
  }

  private async init() {
    this.db = await JSONFilePreset<DatabaseSchema>('db.json', { 
      scrapers: [], 
      priceHistory: [] 
    });
  }

  private async ensureDb() {
    if (!this.db) {
      await this.init();
    }
    return this.db!;
  }

  async getScraper(id: string): Promise<Scraper | undefined> {
    const db = await this.ensureDb();
    return db.data.scrapers.find(scraper => scraper.id === id);
  }

  async getAllScrapers(): Promise<Scraper[]> {
    const db = await this.ensureDb();
    return db.data.scrapers;
  }

  async createScraper(insertScraper: InsertScraper): Promise<Scraper> {
    const db = await this.ensureDb();
    
    const id = randomUUID();
    const scraper: Scraper = {
      ...insertScraper,
      id,
      currentPrice: null,
      lowestPrice: null,
      status: "active",
      lastUpdated: new Date(),
      lastError: null,
      createdAt: new Date(),
    };
    
    await db.update(({ scrapers }) => scrapers.push(scraper));
    return scraper;
  }

  async updateScraper(id: string, updates: UpdateScraper): Promise<Scraper | undefined> {
    const db = await this.ensureDb();
    
    const scraperIndex = db.data.scrapers.findIndex(scraper => scraper.id === id);
    if (scraperIndex === -1) return undefined;

    const updatedScraper: Scraper = {
      ...db.data.scrapers[scraperIndex],
      ...updates,
      lastUpdated: new Date(),
    };
    
    await db.update(({ scrapers }) => {
      scrapers[scraperIndex] = updatedScraper;
    });
    
    return updatedScraper;
  }

  async deleteScraper(id: string): Promise<boolean> {
    const db = await this.ensureDb();
    
    const scraperIndex = db.data.scrapers.findIndex(scraper => scraper.id === id);
    if (scraperIndex === -1) return false;

    await db.update(({ scrapers, priceHistory }) => {
      scrapers.splice(scraperIndex, 1);
      // Remove associated price history
      const historyIndicesToRemove = [];
      for (let i = priceHistory.length - 1; i >= 0; i--) {
        if (priceHistory[i].scraperId === id) {
          historyIndicesToRemove.push(i);
        }
      }
      for (const index of historyIndicesToRemove) {
        priceHistory.splice(index, 1);
      }
    });
    
    return true;
  }

  async addPriceHistory(scraperId: string, price: string): Promise<void> {
    const db = await this.ensureDb();
    
    const newEntry: PriceHistory = {
      id: randomUUID(),
      scraperId,
      price,
      timestamp: new Date(),
    };
    
    await db.update(({ priceHistory }) => priceHistory.push(newEntry));
  }

  async getPriceHistory(scraperId: string): Promise<PriceHistory[]> {
    const db = await this.ensureDb();
    return db.data.priceHistory.filter(entry => entry.scraperId === scraperId);
  }
}

export const storage = new JsonStorage();
