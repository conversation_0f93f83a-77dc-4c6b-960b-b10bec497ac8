import type { Server as HttpServer } from "http";
import { Server, Socket } from "socket.io";
import { storage } from "../storage";
import { getAdapter, PRIMARY_ADAPTER_NAME, FALLBACK_ADAPTER_NAME } from "./adapters";
import type { Scraper } from "@shared/schema";

export function setupSockets(server: HttpServer) {
  const io = new Server(server, {
    cors: {
      origin: "*", // Configure this for production
    },
  });

  io.on("connection", (socket: Socket) => {
    console.log(`Client connected: ${socket.id}`);

    socket.on("disconnect", () => {
      console.log(`Client disconnected: ${socket.id}`);
    });

    // Handler for updating a single scraper
    socket.on("scraper:update", async (id: string) => {
      await processScraperUpdate(id, socket);
    });

    // Handler for updating all scrapers
    socket.on("scraper:update-all", async () => {
      const scrapers = await storage.getAllScrapers();
      // Process all updates in parallel
      for (const scraper of scrapers) {
        processScraperUpdate(scraper.id, socket);
      }
    });
  });
}

async function processScraperUpdate(id: string, socket: Socket) {
  let scraper = await storage.getScraper(id);
  if (!scraper) {
    socket.emit("scraper:error", { id, message: "Scraper not found" });
    return;
  }

  // 1. Set status to "updating" and notify client
  await storage.updateScraper(id, { status: "updating" });
  let updatedScraper = await storage.getScraper(id);
  socket.emit("scraper:updated", updatedScraper);

  // 2. Try primary adapter
  const primaryAdapter = getAdapter(PRIMARY_ADAPTER_NAME);
  let result = await primaryAdapter!.scrape(scraper.url, scraper.selector);

  // 3. If primary fails, try fallback adapter
  if (!result.price) {
    console.log(`Primary adapter failed for ${scraper.url}. Reason: ${result.error}. Trying fallback.`);
    const fallbackAdapter = getAdapter(FALLBACK_ADAPTER_NAME);
    result = await fallbackAdapter!.scrape(scraper.url, scraper.selector);
  }

  // 4. Process the final result
  if (result.price) {
    const currentPrice = parseFloat(result.price);
    const lowestPrice = scraper.lowestPrice
      ? Math.min(parseFloat(scraper.lowestPrice), currentPrice)
      : currentPrice;

    await storage.updateScraper(id, {
      currentPrice: result.price,
      lowestPrice: lowestPrice.toString(),
      status: "active",
      lastError: null,
    });
    await storage.addPriceHistory(id, result.price);
  } else {
    await storage.updateScraper(id, {
      status: "error",
      lastError: result.error || "Both scraping methods failed.",
    });
  }

  // 5. Send the final state to the client
  updatedScraper = await storage.getScraper(id);
  socket.emit("scraper:updated", updatedScraper);
}
