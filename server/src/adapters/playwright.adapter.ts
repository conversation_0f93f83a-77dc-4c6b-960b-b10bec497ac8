import { chromium } from "playwright";
import * as cheerio from "cheerio";
import type { <PERSON>raper<PERSON><PERSON><PERSON><PERSON>, ScrapeResult } from "./types";

function extractPrice(priceText: string): string | null {
  const priceMatch = priceText.match(/[\d,]+\.?\d*/);
  if (!priceMatch) return null;
  
  const price = priceMatch[0].replace(/,/g, '');
  const numericPrice = parseFloat(price);
  
  return isNaN(numericPrice) ? null : numericPrice.toString();
}

export const playwrightAdapter: ScraperAdapter = {
  name: 'playwright',
  async scrape(url: string, selector: string): Promise<ScrapeResult> {
    let browser;
    try {
      browser = await chromium.launch();
      const page = await browser.newPage();
      await page.goto(url, { waitUntil: 'networkidle', timeout: 30000 });

      // Wait for the selector to appear on the page
      await page.waitForSelector(selector, { timeout: 15000 });

      const html = await page.content();
      const $ = cheerio.load(html);
      
      const priceElement = $(selector);
      if (priceElement.length === 0) {
        return { price: null, error: `Selector "${selector}" not found on the page after JS execution` };
      }
      
      const priceText = priceElement.first().text().trim();
      const price = extractPrice(priceText);

      if (!price) {
        return { price: null, error: `Could not extract a valid price from text: "${priceText}"` };
      }
      
      return { price };
    } catch (error) {
      return { price: null, error: error instanceof Error ? error.message : 'Unknown error occurred' };
    } finally {
      if (browser) {
        await browser.close();
      }
    }
  }
};
