import { chromium } from "playwright";
import * as cheerio from "cheerio";
import type { <PERSON><PERSON>er<PERSON><PERSON>pt<PERSON>, ScrapeR<PERSON>ult } from "./types";

function extractPrice(priceText: string): string | null {
  const priceMatch = priceText.match(/[\d,]+\.?\d*/);
  if (!priceMatch) return null;
  
  const price = priceMatch[0].replace(/,/g, '');
  const numericPrice = parseFloat(price);
  
  return isNaN(numericPrice) ? null : numericPrice.toString();
}

export const playwrightAdapter: ScraperAdapter = {
  name: 'playwright',
  async scrape(url: string, selector: string): Promise<ScrapeResult> {
    let browser;
    try {
      browser = await chromium.launch({
        headless: true,
        args: [
          '--no-sandbox',
          '--disable-setuid-sandbox',
          '--disable-dev-shm-usage',
          '--disable-accelerated-2d-canvas',
          '--no-first-run',
          '--no-zygote',
          '--disable-gpu'
        ]
      });

      const page = await browser.newPage();

      // Set a realistic user agent
      await page.setUserAgent('Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36');

      // Set viewport
      await page.setViewportSize({ width: 1280, height: 720 });

      // Try multiple wait strategies
      try {
        // First try with domcontentloaded (faster)
        await page.goto(url, { waitUntil: 'domcontentloaded', timeout: 20000 });

        // Wait a bit for dynamic content
        await page.waitForTimeout(2000);

        // Try to wait for the selector with a reasonable timeout
        await page.waitForSelector(selector, { timeout: 10000 });
      } catch (selectorError) {
        console.log(`Playwright: Selector wait failed, trying alternative approach for ${url}`);

        // If selector wait fails, try with load event and longer wait
        try {
          await page.goto(url, { waitUntil: 'load', timeout: 25000 });
          await page.waitForTimeout(3000);
        } catch (loadError) {
          console.log(`Playwright: Load event failed, using basic navigation for ${url}`);
          // Last resort: just navigate and wait
          await page.goto(url, { timeout: 30000 });
          await page.waitForTimeout(5000);
        }
      }

      const html = await page.content();
      const $ = cheerio.load(html);

      const priceElement = $(selector);
      if (priceElement.length === 0) {
        return { price: null, error: `Selector "${selector}" not found on the page after JS execution` };
      }

      const priceText = priceElement.first().text().trim();
      const price = extractPrice(priceText);

      if (!price) {
        return { price: null, error: `Could not extract a valid price from text: "${priceText}"` };
      }

      return { price };
    } catch (error) {
      return { price: null, error: error instanceof Error ? error.message : 'Unknown error occurred' };
    } finally {
      if (browser) {
        await browser.close();
      }
    }
  }
};
