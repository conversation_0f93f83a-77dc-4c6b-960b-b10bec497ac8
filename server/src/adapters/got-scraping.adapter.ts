import { gotScraping } from "got-scraping";
import * as cheerio from "cheerio";
import type { <PERSON>raperAdapt<PERSON>, ScrapeR<PERSON>ult } from "./types";

function extractPrice(priceText: string): string | null {
  const priceMatch = priceText.match(/[\d,]+\.?\d*/);
  if (!priceMatch) return null;
  
  const price = priceMatch[0].replace(/,/g, '');
  const numericPrice = parseFloat(price);
  
  return isNaN(numericPrice) ? null : numericPrice.toString();
}

export const gotScrapingAdapter: ScraperAdapter = {
  name: 'gotScraping',
  async scrape(url: string, selector: string): Promise<ScrapeResult> {
    try {
      const response = await gotScraping.get(url, { timeout: { request: 10000 } });
      const html = response.body;
      const $ = cheerio.load(html);
      
      const priceElement = $(selector);
      if (priceElement.length === 0) {
        // It's not an error, the selector just might not be in the initial HTML
        return { price: null };
      }
      
      const priceText = priceElement.first().text().trim();
      const price = extractPrice(priceText);

      if (!price) {
        return { price: null, error: `Could not extract a valid price from text: "${priceText}"` };
      }
      
      return { price };
    } catch (error) {
      return { price: null, error: error instanceof Error ? error.message : 'Unknown error occurred' };
    }
  }
};
