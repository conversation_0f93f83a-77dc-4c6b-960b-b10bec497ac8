import { gotScraping } from "got-scraping";
import * as cheerio from "cheerio";
import type { ScraperAdapter, ScrapeR<PERSON>ult } from "./types";

function extractPrice(priceText: string): string | null {
  const priceMatch = priceText.match(/[\d,]+\.?\d*/);
  if (!priceMatch) return null;
  
  const price = priceMatch[0].replace(/,/g, '');
  const numericPrice = parseFloat(price);
  
  return isNaN(numericPrice) ? null : numericPrice.toString();
}

export const gotScrapingAdapter: ScraperAdapter = {
  name: 'gotScraping',
  async scrape(url: string, selector: string): Promise<ScrapeResult> {
    try {
      const response = await gotScraping.get(url, {
        timeout: { request: 15000 }, // Increased timeout
        headers: {
          'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
          'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
          'Accept-Language': 'en-US,en;q=0.5',
          'Accept-Encoding': 'gzip, deflate, br',
          'DNT': '1',
          'Connection': 'keep-alive',
          'Upgrade-Insecure-Requests': '1',
        },
        retry: {
          limit: 2,
          methods: ['GET']
        },
        followRedirect: true,
        maxRedirects: 5
      });

      const html = response.body;
      const $ = cheerio.load(html);

      const priceElement = $(selector);
      if (priceElement.length === 0) {
        // It's not an error, the selector just might not be in the initial HTML
        return { price: null, error: `Selector "${selector}" not found in initial HTML` };
      }

      const priceText = priceElement.first().text().trim();
      const price = extractPrice(priceText);

      if (!price) {
        return { price: null, error: `Could not extract a valid price from text: "${priceText}"` };
      }

      return { price };
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred';
      console.log(`Got-scraping failed for ${url}: ${errorMessage}`);
      return { price: null, error: errorMessage };
    }
  }
};
