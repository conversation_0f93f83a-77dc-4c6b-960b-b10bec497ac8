import { parentPort, workerData } from "worker_threads";
import { getAdapter } from "../adapters";
import type { ScrapeResult } from "../adapters/types";

interface WorkerJobData {
  id: string;
  url: string;
  selector: string;
  primaryAdapter: string;
  fallbackAdapter: string;
}

async function run() {
  if (!parentPort) throw new Error("This script must be run as a worker thread.");

  const { id, url, selector, primaryAdapter, fallbackAdapter }: WorkerJobData = workerData;

  console.log(`Worker processing job ${id}: ${url}`);

  try {
    // Step 1: Try primary adapter (got-scraping)
    console.log(`Job ${id}: Trying primary adapter (${primaryAdapter})`);
    const primaryAdapterInstance = getAdapter(primaryAdapter);

    if (!primaryAdapterInstance) {
      throw new Error(`Primary adapter "${primaryAdapter}" not found.`);
    }

    let result: ScrapeResult = await primaryAdapterInstance.scrape(url, selector);

    // Step 2: If primary fails, try fallback adapter (playwright)
    if (!result.price) {
      console.log(`Job ${id}: Primary adapter failed (${result.error || 'no price found'}). Trying fallback adapter (${fallbackAdapter})`);

      const fallbackAdapterInstance = getAdapter(fallbackAdapter);
      if (!fallbackAdapterInstance) {
        throw new Error(`Fallback adapter "${fallbackAdapter}" not found.`);
      }

      result = await fallbackAdapterInstance.scrape(url, selector);

      // Step 3: If both adapters fail, return final error
      if (!result.price) {
        console.log(`Job ${id}: Both adapters failed. Final error: ${result.error || 'no price found'}`);
        result = {
          price: null,
          error: `Both ${primaryAdapter} and ${fallbackAdapter} failed to extract price. Last error: ${result.error || 'no price found'}`,
        };
      } else {
        console.log(`Job ${id}: Fallback adapter succeeded, price: ${result.price}`);
      }
    } else {
      console.log(`Job ${id}: Primary adapter succeeded, price: ${result.price}`);
    }

    // Send result back to main thread
    parentPort.postMessage(result);

  } catch (error) {
    console.error(`Job ${id}: Worker execution failed:`, error);

    const result: ScrapeResult = {
      price: null,
      error: error instanceof Error ? error.message : 'Worker execution failed',
    };

    parentPort.postMessage(result);
  }
}

run();
