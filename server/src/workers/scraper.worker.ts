import { parentPort, workerData } from "worker_threads";
import { getAdapter } from "../adapters";
import type { ScrapeResult } from "../adapters/types";

interface WorkerInput {
  url: string;
  selector: string;
  adapterName: string;
}

async function run() {
  if (!parentPort) throw new Error("This script must be run as a worker thread.");

  const { url, selector, adapterName }: WorkerInput = workerData;
  
  const adapter = getAdapter(adapterName);
  if (!adapter) {
    throw new Error(`Scraper adapter "${adapterName}" not found.`);
  }

  try {
    const result: ScrapeResult = await adapter.scrape(url, selector);
    parentPort.postMessage(result);
  } catch (error) {
    const result: ScrapeResult = {
      price: null,
      error: error instanceof Error ? error.message : 'Worker execution failed',
    };
    parentPort.postMessage(result);
  } finally {
    // In a Piscina-based pool, we don't need to manually exit.
    // For a simple worker, process.exit() might be used after the task.
  }
}

run();
