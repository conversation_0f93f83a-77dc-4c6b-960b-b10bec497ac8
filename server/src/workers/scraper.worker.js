// server/src/workers/scraper.worker.ts
import { parentPort, workerData } from "worker_threads";

// server/src/adapters/got-scraping.adapter.ts
import { gotScraping } from "got-scraping";
import * as cheerio from "cheerio";
function extractPrice(priceText) {
  const priceMatch = priceText.match(/[\d,]+\.?\d*/);
  if (!priceMatch) return null;
  const price = priceMatch[0].replace(/,/g, "");
  const numericPrice = parseFloat(price);
  return isNaN(numericPrice) ? null : numericPrice.toString();
}
var gotScrapingAdapter = {
  name: "gotScraping",
  async scrape(url, selector) {
    try {
      const response = await gotScraping.get(url, {
        timeout: { request: 15e3 }
        // Only increase timeout, let got-scraping handle the rest
      });
      const html = response.body;
      const $ = cheerio.load(html);
      const priceElement = $(selector);
      if (priceElement.length === 0) {
        return { price: null };
      }
      const priceText = priceElement.first().text().trim();
      const price = extractPrice(priceText);
      if (!price) {
        return { price: null, error: `Could not extract a valid price from text: "${priceText}"` };
      }
      return { price };
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : "Unknown error occurred";
      console.log(`Got-scraping failed for ${url}: ${errorMessage}`);
      return { price: null, error: errorMessage };
    }
  }
};

// server/src/adapters/playwright.adapter.ts
import { chromium } from "playwright";
import * as cheerio2 from "cheerio";
function extractPrice2(priceText) {
  const priceMatch = priceText.match(/[\d,]+\.?\d*/);
  if (!priceMatch) return null;
  const price = priceMatch[0].replace(/,/g, "");
  const numericPrice = parseFloat(price);
  return isNaN(numericPrice) ? null : numericPrice.toString();
}
var playwrightAdapter = {
  name: "playwright",
  async scrape(url, selector) {
    let browser;
    try {
      browser = await chromium.launch({
        headless: true,
        args: [
          "--no-sandbox",
          "--disable-setuid-sandbox",
          "--disable-dev-shm-usage",
          "--disable-accelerated-2d-canvas",
          "--no-first-run",
          "--no-zygote",
          "--disable-gpu"
        ]
      });
      const page = await browser.newPage({
        userAgent: "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36",
        viewport: { width: 1280, height: 720 }
      });
      try {
        console.log(`Playwright: Navigating to ${url} with domcontentloaded`);
        await page.goto(url, { waitUntil: "domcontentloaded", timeout: 2e4 });
        await page.waitForTimeout(2e3);
        console.log(`Playwright: Waiting for selector "${selector}"`);
        await page.waitForSelector(selector, { timeout: 1e4 });
        console.log(`Playwright: Selector found successfully`);
      } catch (selectorError) {
        const selectorErrorMsg = selectorError instanceof Error ? selectorError.message : String(selectorError);
        console.log(`Playwright: Selector wait failed (${selectorErrorMsg}), trying alternative approach for ${url}`);
        try {
          console.log(`Playwright: Trying with load event`);
          await page.goto(url, { waitUntil: "load", timeout: 25e3 });
          await page.waitForTimeout(3e3);
        } catch (loadError) {
          const loadErrorMsg = loadError instanceof Error ? loadError.message : String(loadError);
          console.log(`Playwright: Load event failed (${loadErrorMsg}), using basic navigation for ${url}`);
          try {
            await page.goto(url, { timeout: 3e4 });
            await page.waitForTimeout(5e3);
          } catch (basicError) {
            const basicErrorMsg = basicError instanceof Error ? basicError.message : String(basicError);
            console.log(`Playwright: Basic navigation failed (${basicErrorMsg})`);
            throw basicError;
          }
        }
      }
      const html = await page.content();
      const $ = cheerio2.load(html);
      const priceElement = $(selector);
      if (priceElement.length === 0) {
        return { price: null, error: `Selector "${selector}" not found on the page after JS execution` };
      }
      const priceText = priceElement.first().text().trim();
      const price = extractPrice2(priceText);
      if (!price) {
        return { price: null, error: `Could not extract a valid price from text: "${priceText}"` };
      }
      return { price };
    } catch (error) {
      return { price: null, error: error instanceof Error ? error.message : "Unknown error occurred" };
    } finally {
      if (browser) {
        await browser.close();
      }
    }
  }
};

// server/src/adapters/index.ts
var adapters = /* @__PURE__ */ new Map();
adapters.set(gotScrapingAdapter.name, gotScrapingAdapter);
adapters.set(playwrightAdapter.name, playwrightAdapter);
function getAdapter(name) {
  return adapters.get(name);
}
var PRIMARY_ADAPTER_NAME = gotScrapingAdapter.name;
var FALLBACK_ADAPTER_NAME = playwrightAdapter.name;

// server/src/workers/scraper.worker.ts
async function run() {
  if (!parentPort) throw new Error("This script must be run as a worker thread.");
  const { id, url, selector, primaryAdapter, fallbackAdapter } = workerData;
  console.log(`Worker processing job ${id}: ${url}`);
  try {
    console.log(`Job ${id}: Trying primary adapter (${primaryAdapter})`);
    const primaryAdapterInstance = getAdapter(primaryAdapter);
    if (!primaryAdapterInstance) {
      throw new Error(`Primary adapter "${primaryAdapter}" not found.`);
    }
    let result = await primaryAdapterInstance.scrape(url, selector);
    if (!result.price) {
      console.log(`Job ${id}: Primary adapter failed (${result.error || "no price found"}). Trying fallback adapter (${fallbackAdapter})`);
      const fallbackAdapterInstance = getAdapter(fallbackAdapter);
      if (!fallbackAdapterInstance) {
        throw new Error(`Fallback adapter "${fallbackAdapter}" not found.`);
      }
      result = await fallbackAdapterInstance.scrape(url, selector);
      if (!result.price) {
        console.log(`Job ${id}: Both adapters failed. Final error: ${result.error || "no price found"}`);
        result = {
          price: null,
          error: `Both ${primaryAdapter} and ${fallbackAdapter} failed to extract price. Last error: ${result.error || "no price found"}`
        };
      } else {
        console.log(`Job ${id}: Fallback adapter succeeded, price: ${result.price}`);
      }
    } else {
      console.log(`Job ${id}: Primary adapter succeeded, price: ${result.price}`);
    }
    parentPort.postMessage(result);
  } catch (error) {
    console.error(`Job ${id}: Worker execution failed:`, error);
    const result = {
      price: null,
      error: error instanceof Error ? error.message : "Worker execution failed"
    };
    parentPort.postMessage(result);
  }
}
run();
