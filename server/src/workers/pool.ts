import { Worker } from "worker_threads";
import path from "path";
import os from "os";
import { fileURLToPath } from "url";
import type { ScrapeResult } from "../adapters/types";

const __dirname = path.dirname(fileURLToPath(import.meta.url));

const MAX_WORKERS = os.availableParallelism();
const WORKER_PATH = path.join(__dirname, "scraper.worker.js"); // Use .js for worker threads

interface Task {
  workerData: {
    url: string;
    selector: string;
    adapterName: string;
  };
  resolve: (value: ScrapeResult) => void;
  reject: (reason?: any) => void;
}

class WorkerPool {
  private queue: Task[] = [];
  private workers: Worker[] = [];
  private activeWorkers: boolean[] = [];

  constructor() {
    for (let i = 0; i < MAX_WORKERS; i++) {
      this.workers[i] = this.createWorker(i);
      this.activeWorkers[i] = false;
    }
  }

  private createWorker(index: number): Worker {
    const worker = new Worker(WORKER_PATH);

    worker.on("message", (result: ScrapeResult) => {
      this.onMessage(index, result);
    });

    worker.on("error", (err) => {
      console.error(`Worker ${index} error:`, err);
      this.onError(index, err);
    });
    
    worker.on("exit", (code) => {
      if (code !== 0) {
        console.error(`Worker ${index} stopped with exit code ${code}`);
      }
      // Optionally restart the worker
      this.workers[index] = this.createWorker(index);
      this.activeWorkers[index] = false;
      this.checkQueue();
    });

    return worker;
  }
  
  private onMessage(workerIndex: number, result: ScrapeResult) {
    const task = this.queue.shift();
    if (task) {
      task.resolve(result);
    }
    this.activeWorkers[workerIndex] = false;
    this.checkQueue();
  }

  private onError(workerIndex: number, err: Error) {
    const task = this.queue.shift();
    if (task) {
      task.reject(err);
    }
    this.activeWorkers[workerIndex] = false;
    this.checkQueue();
  }

  private checkQueue() {
    if (this.queue.length > 0) {
      const availableWorkerIndex = this.activeWorkers.findIndex(active => !active);
      if (availableWorkerIndex !== -1) {
        const task = this.queue[0]; // Peek at the next task
        this.activeWorkers[availableWorkerIndex] = true;
        this.workers[availableWorkerIndex].postMessage(task.workerData);
      }
    }
  }

  run(workerData: Task['workerData']): Promise<ScrapeResult> {
    return new Promise((resolve, reject) => {
      this.queue.push({ workerData, resolve, reject });
      this.checkQueue();
    });
  }
}

export const scraperWorkerPool = new WorkerPool();
