import { Worker } from "worker_threads";
import path from "path";
import os from "os";
import { fileURLToPath } from "url";
import fastq from "fastq";
import type { queueAsPromised } from "fastq";
import type { ScrapeResult } from "../adapters/types";
import { PRIMARY_ADAPTER_NAME, FALLBACK_ADAPTER_NAME } from "../adapters";

const __dirname = path.dirname(fileURLToPath(import.meta.url));

const MAX_WORKERS = os.availableParallelism();
const WORKER_PATH = path.join(__dirname, "scraper.worker.js"); // Use .js for worker threads

// Job interface for the queue
export interface ScrapeJob {
  id: string;
  url: string;
  selector: string;
  priority?: 'high' | 'normal'; // high for single updates, normal for bulk
  onProgress?: (status: string) => void;
  onComplete?: (result: ScrapeResult) => void;
  onError?: (error: Error) => void;
}

// Internal job data passed to worker
interface WorkerJobData {
  id: string;
  url: string;
  selector: string;
  primaryAdapter: string;
  fallbackAdapter: string;
}

class ScraperQueueManager {
  private queue: queueAsPromised<ScrapeJob>;
  private workers: Map<number, Worker> = new Map();
  private activeJobs: Map<string, ScrapeJob> = new Map();

  constructor() {
    // Create fastq queue with worker function and concurrency
    this.queue = fastq.promise(this.processJob.bind(this), MAX_WORKERS);

    // Set up queue event handlers
    this.queue.error((err, job) => {
      console.error(`Queue error for job ${job?.id}:`, err);
      if (job?.onError) {
        job.onError(err instanceof Error ? err : new Error(String(err)));
      }
    });

    this.queue.drain = () => {
      console.log('All scraping jobs completed');
    };

    console.log(`ScraperQueueManager initialized with ${MAX_WORKERS} workers`);
  }

  // Process a single job using worker thread
  private async processJob(job: ScrapeJob): Promise<ScrapeResult> {
    console.log(`Processing scrape job ${job.id} for ${job.url}`);

    // Store active job for tracking
    this.activeJobs.set(job.id, job);

    try {
      // Notify progress
      if (job.onProgress) {
        job.onProgress('starting');
      }

      // Create worker data
      const workerData: WorkerJobData = {
        id: job.id,
        url: job.url,
        selector: job.selector,
        primaryAdapter: PRIMARY_ADAPTER_NAME,
        fallbackAdapter: FALLBACK_ADAPTER_NAME,
      };

      // Execute job in worker thread
      const result = await this.executeInWorker(workerData);

      // Notify completion
      if (job.onComplete) {
        job.onComplete(result);
      }

      return result;
    } catch (error) {
      console.error(`Job ${job.id} failed:`, error);
      const err = error instanceof Error ? error : new Error(String(error));

      if (job.onError) {
        job.onError(err);
      }

      // Return error result instead of throwing
      return {
        price: null,
        error: err.message,
      };
    } finally {
      // Clean up
      this.activeJobs.delete(job.id);
    }
  }

  // Execute job in worker thread
  private async executeInWorker(workerData: WorkerJobData): Promise<ScrapeResult> {
    return new Promise((resolve, reject) => {
      const worker = new Worker(WORKER_PATH, { workerData });

      const timeout = setTimeout(() => {
        worker.terminate();
        reject(new Error(`Worker timeout for job ${workerData.id}`));
      }, 60000); // 60 second timeout

      worker.on("message", (result: ScrapeResult) => {
        clearTimeout(timeout);
        worker.terminate();
        resolve(result);
      });

      worker.on("error", (err) => {
        clearTimeout(timeout);
        worker.terminate();
        reject(err);
      });

      worker.on("exit", (code) => {
        clearTimeout(timeout);
        if (code !== 0) {
          reject(new Error(`Worker stopped with exit code ${code}`));
        }
      });
    });
  }

  // Public API: Add a scraping job to the queue
  async addJob(job: ScrapeJob): Promise<ScrapeResult> {
    console.log(`Adding scrape job ${job.id} to queue (priority: ${job.priority || 'normal'})`);

    // For high priority jobs, add to front of queue
    if (job.priority === 'high') {
      return this.queue.unshift(job);
    } else {
      return this.queue.push(job);
    }
  }

  // Public API: Get queue statistics
  getStats() {
    return {
      queueLength: this.queue.length(),
      activeJobs: this.activeJobs.size,
      isIdle: this.queue.idle(),
      concurrency: this.queue.concurrency,
    };
  }

  // Public API: Wait for all jobs to complete
  async drain(): Promise<void> {
    return this.queue.drained();
  }

  // Public API: Graceful shutdown
  async shutdown(): Promise<void> {
    console.log('Shutting down scraper queue manager...');

    // Wait for current jobs to complete
    await this.queue.drained();

    // Kill any remaining workers
    this.workers.forEach(worker => {
      worker.terminate();
    });

    this.workers.clear();
    this.activeJobs.clear();

    console.log('Scraper queue manager shutdown complete');
  }
}

// Export singleton instance
export const scraperQueue = new ScraperQueueManager();
