import { sql } from "drizzle-orm";
import { pgTable, text, varchar, decimal, integer, timestamp, boolean } from "drizzle-orm/pg-core";
import { createInsertSchema } from "drizzle-zod";
import { z } from "zod";

export const scrapers = pgTable("scrapers", {
  id: varchar("id").primaryKey().default(sql`gen_random_uuid()`),
  itemName: text("item_name").notNull(),
  url: text("url").notNull(),
  selector: text("selector").notNull(),
  currentPrice: decimal("current_price", { precision: 10, scale: 2 }),
  lowestPrice: decimal("lowest_price", { precision: 10, scale: 2 }),
  status: varchar("status", { length: 20 }).notNull().default("active"), // active, updating, error
  lastUpdated: timestamp("last_updated").defaultNow(),
  lastError: text("last_error"),
  createdAt: timestamp("created_at").defaultNow(),
});

export const priceHistory = pgTable("price_history", {
  id: varchar("id").primaryKey().default(sql`gen_random_uuid()`),
  scraperId: varchar("scraper_id").notNull().references(() => scrapers.id, { onDelete: "cascade" }),
  price: decimal("price", { precision: 10, scale: 2 }).notNull(),
  timestamp: timestamp("timestamp").defaultNow(),
});

export const insertScraperSchema = createInsertSchema(scrapers).omit({
  id: true,
  currentPrice: true,
  lowestPrice: true,
  status: true,
  lastUpdated: true,
  lastError: true,
  createdAt: true,
});

export const updateScraperSchema = createInsertSchema(scrapers).omit({
  id: true,
  createdAt: true,
}).partial();

export type InsertScraper = z.infer<typeof insertScraperSchema>;
export type UpdateScraper = z.infer<typeof updateScraperSchema>;
export type Scraper = typeof scrapers.$inferSelect;
export type PriceHistory = typeof priceHistory.$inferSelect;
